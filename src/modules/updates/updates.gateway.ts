import { Inject, Logger, forwardRef } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import {
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';

import { Role } from '@/common/enums';
import { Forwarded } from '@/common/types';

import { UpdatesRoomsService } from './updates-rooms.service';
import {
  UpdatesEventPayloads,
  UpdatesEvents,
  UpdatesMessageBody,
} from './updates.types';
import { WsAuthMiddleware } from './ws-auth.middleware';
import { RequestUserType } from '../auth/dto/request-user.dto';
import { SessionsService } from '../sessions/sessions.service';
import { UsersService } from '../users/users.service';
import { WorkersService } from '../workers/workers.service';

type AuthenticatedSocket = Socket & {
  user: RequestUserType;
};

@WebSocketGateway({
  cors: {
    origin: true,
    credentials: true,
  },
  transports: ['websocket', 'polling'],
  namespace: '/updates',
  pingTimeout: 10000,
  pingInterval: 25000,
})
export class UpdatesGateway
  implements OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server!: Server;

  private logger: Logger = new Logger('UpdatesGateway');

  constructor(
    private readonly updatesRoomsService: UpdatesRoomsService,
    @Inject(forwardRef(() => WorkersService))
    private readonly workersService: Forwarded<WorkersService>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly sessionsService: SessionsService,
    @Inject(forwardRef(() => UsersService))
    private readonly usersService: Forwarded<UsersService>,
  ) {}

  async afterInit(server: Server) {
    server.use(
      WsAuthMiddleware(
        this.jwtService,
        this.configService,
        this.sessionsService,
        this.usersService,
      ),
    );
  }

  async handleConnection(client: Socket) {
    try {
      const authenticatedClient = client as AuthenticatedSocket;

      if (!authenticatedClient.user) {
        throw new Error('Unauthorized connection attempt');
      }

      const token = client.handshake?.auth?.token;
      if (token) {
        try {
          const payload = await this.jwtService.verifyAsync(token);
          const tokenExp = payload.exp * 1000;
          const timeUntilExp = tokenExp - Date.now();

          if (timeUntilExp > 10000) {
            // Disconnect 10 seconds before token expires
            setTimeout(() => {
              this.logger.log(
                `Disconnecting client ${client.id} due to token expiration`,
              );
              client.disconnect(true);
            }, timeUntilExp - 10000);
          }
        } catch (error) {
          this.logger.warn(
            `Could not parse token expiration for client ${client.id}:`,
            error,
          );
        }
      }

      await this.updatesRoomsService.joinUserRooms(authenticatedClient);
      this.logger.log(`Client connected and joined rooms: ${client.id}`);
    } catch (error) {
      this.logger.error(
        `Error handling connection for client ${client.id}:`,
        error,
      );
      client.disconnect();
    }
  }

  handleDisconnect(client: Socket) {
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  async sendMessage<T extends UpdatesEvents>(message: UpdatesMessageBody<T>) {
    const rooms = await this.determineTargetRooms(message);
    for (const room of rooms) {
      this.server.to(room).emit(message.type, message.payload);
    }
  }

  private async determineTargetRooms<T extends UpdatesEvents>(
    message: UpdatesMessageBody<T>,
  ): Promise<string[]> {
    const rooms = new Set<string>();

    switch (message.type) {
      case UpdatesEvents.USER_UPDATED:
        await this.handleUserUpdate(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.USER_UPDATED>,
        );
        break;

      case UpdatesEvents.WORKER_UPDATED:
        await this.handleWorkerUpdate(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.WORKER_UPDATED>,
        );
        break;

      case UpdatesEvents.WORKER_EMPLOYMENT_CHANGED:
      case UpdatesEvents.WORKER_APPROVAL_CHANGED:
      case UpdatesEvents.WORKER_STATUS_CHANGED:
      case UpdatesEvents.WORKER_PRESENCE_CHANGED:
        await this.handleWorkerStatusUpdate(
          rooms,
          message as UpdatesMessageBody<
            | UpdatesEvents.WORKER_EMPLOYMENT_CHANGED
            | UpdatesEvents.WORKER_APPROVAL_CHANGED
            | UpdatesEvents.WORKER_STATUS_CHANGED
            | UpdatesEvents.WORKER_PRESENCE_CHANGED
          >,
        );
        break;

      case UpdatesEvents.PROJECT_CREATED:
        await this.handleProjectCreated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PROJECT_CREATED>,
        );
        break;

      case UpdatesEvents.PROJECT_DELETED:
        await this.handleProjectDeleted(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PROJECT_DELETED>,
        );
        break;

      case UpdatesEvents.PROJECT_UPDATED:
        await this.handleProjectUpdated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PROJECT_UPDATED>,
        );
        break;

      case UpdatesEvents.PARTNER_UPDATED:
        await this.handlePartnerUpdated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PARTNER_UPDATED>,
        );
        break;

      case UpdatesEvents.DAILY_REPORT_CREATED:
        await this.handleDailyReportCreated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_CREATED>,
        );
        break;

      case UpdatesEvents.DAILY_REPORT_UPDATED:
        await this.handleDailyReportUpdated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_UPDATED>,
        );
        break;

      case UpdatesEvents.DAILY_REPORT_FINISHED:
        await this.handleDailyReportFinished(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_FINISHED>,
        );
        break;

      case UpdatesEvents.PRESENCE_VALIDATION_REQUESTED:
        await this.handlePresenceValidationRequested(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PRESENCE_VALIDATION_REQUESTED>,
        );
        break;

      case UpdatesEvents.REGISTRATION_REQUEST_RECEIVED:
        await this.handleRegistrationRequestReceived(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.REGISTRATION_REQUEST_RECEIVED>,
        );
        break;

      case UpdatesEvents.MANAGER_UPDATED:
        await this.handleManagerUpdated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.MANAGER_UPDATED>,
        );
        break;

      case UpdatesEvents.MANAGER_ASSIGNED_TO_PROJECT:
        await this.handleManagerAssignedToProject(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.MANAGER_ASSIGNED_TO_PROJECT>,
        );
        break;

      case UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT:
        await this.handleManagerRemovedFromProject(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT>,
        );
        break;

      case UpdatesEvents.USER_DELETED:
        await this.handleUserDeleted(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.USER_DELETED>,
        );
        break;
      case UpdatesEvents.MANAGER_EMPLOYMENT_CHANGED:
        await this.handleManagerEmploymentChanged(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.MANAGER_EMPLOYMENT_CHANGED>,
        );
        break;

      case UpdatesEvents.PROJECT_MANAGER_UPDATED:
        await this.handleProjectManagerUpdated(
          rooms,
          message as UpdatesMessageBody<UpdatesEvents.PROJECT_MANAGER_UPDATED>,
        );
        break;

      default:
        this.logger.warn(`Unhandled event type: ${message.type}`);
        break;
    }
    return Array.from(rooms);
  }

  private async handleUserUpdate(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.USER_UPDATED>,
  ) {
    const userUpdate =
      message.payload as UpdatesEventPayloads[UpdatesEvents.USER_UPDATED];
    const userId = userUpdate.userId;

    rooms.add(`user:${userId}`);

    const userRoleInfo = await this.usersService.getUserRoleInfo(userId);
    if (!userRoleInfo?.entityId) return;

    rooms.add(`${userRoleInfo.type}:${userRoleInfo.entityId}`);

    if (userRoleInfo.type === Role.Worker) {
      await this.addWorkerRelatedRooms(rooms, userRoleInfo.entityId);

      this.emitWorkerInfoChanged(userRoleInfo.entityId, userUpdate.changes);
    }
  }

  private async handleWorkerUpdate(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.WORKER_UPDATED>,
  ) {
    const workerUpdate =
      message.payload as UpdatesEventPayloads[UpdatesEvents.WORKER_UPDATED];
    const workerDetails = await this.workersService.findOne(
      workerUpdate.workerId,
    );
    if (!workerDetails) return;

    rooms.add(`worker:${workerUpdate.workerId}`);
    rooms.add(`user:${workerDetails.userId}`);

    await this.addWorkerRelatedRooms(rooms, workerUpdate.workerId);
    await this.handleProjectAssignment(rooms, workerUpdate);
  }

  private async handleWorkerStatusUpdate(
    rooms: Set<string>,
    message: UpdatesMessageBody<
      | UpdatesEvents.WORKER_EMPLOYMENT_CHANGED
      | UpdatesEvents.WORKER_APPROVAL_CHANGED
      | UpdatesEvents.WORKER_STATUS_CHANGED
      | UpdatesEvents.WORKER_PRESENCE_CHANGED
    >,
  ) {
    const workerId = (message.payload as { workerId: string }).workerId;
    rooms.add(`worker:${workerId}`);
    await this.addWorkerRelatedRooms(rooms, workerId);
  }

  private async handleProjectCreated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PROJECT_CREATED>,
  ) {
    const { projectId, partnerId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.PROJECT_CREATED];
    rooms.add(`project:${projectId}`);
    rooms.add(`partner:${partnerId}`);
    this.server.in(`partner:${partnerId}`).socketsJoin(`project:${projectId}`);
  }

  private async handleProjectDeleted(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PROJECT_DELETED>,
  ) {
    const { projectId, partnerId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.PROJECT_DELETED];
    rooms.add(`project:${projectId}`);
    rooms.add(`partner:${partnerId}`);
    this.server.in(`project:${projectId}`).socketsLeave(`project:${projectId}`);
  }

  private async handleProjectUpdated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PROJECT_UPDATED>,
  ) {
    const { projectId, partnerId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.PROJECT_UPDATED];
    rooms.add(`project:${projectId}`);
    if (partnerId) {
      rooms.add(`partner:${partnerId}`);
    }
  }

  private async handlePartnerUpdated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PARTNER_UPDATED>,
  ) {
    const { partnerId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.PARTNER_UPDATED];
    rooms.add(`partner:${partnerId}`);
  }

  private async addWorkerRelatedRooms(rooms: Set<string>, workerId: string) {
    const workerDetails = await this.workersService.findOne(workerId);
    if (!workerDetails) return;

    if (workerDetails.projectId) {
      rooms.add(`project:${workerDetails.projectId}`);
    }
    if (workerDetails.partnerId) {
      rooms.add(`partner:${workerDetails.partnerId}`);
    }
  }

  private async handleProjectAssignment(
    rooms: Set<string>,
    workerUpdate: UpdatesEventPayloads[UpdatesEvents.WORKER_UPDATED],
  ) {
    if (!workerUpdate.projectId) return;

    rooms.add(`project:${workerUpdate.projectId}`);

    if (workerUpdate.action === 'project_assigned') {
      this.server
        .in(`worker:${workerUpdate.workerId}`)
        .socketsJoin(`project:${workerUpdate.projectId}`);
    } else if (workerUpdate.action === 'project_unassigned') {
      this.server
        .in(`worker:${workerUpdate.workerId}`)
        .socketsLeave(`project:${workerUpdate.projectId}`);
    }
  }

  private async handleDailyReportCreated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_CREATED>,
  ) {
    const { reportId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.DAILY_REPORT_CREATED];
    // Daily reports are typically associated with workers, but we need more context
    // For now, we'll emit to all connected users who might be interested
    // This should be refined based on business logic
    this.logger.log(`Daily report created: ${reportId}`);
  }

  private async handleDailyReportUpdated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_UPDATED>,
  ) {
    const { reportId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.DAILY_REPORT_UPDATED];
    // Similar to created, needs business logic to determine target rooms
    this.logger.log(`Daily report updated: ${reportId}`);
  }

  private async handleDailyReportFinished(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.DAILY_REPORT_FINISHED>,
  ) {
    const { workerId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.DAILY_REPORT_FINISHED];
    rooms.add(`worker:${workerId}`);
    await this.addWorkerRelatedRooms(rooms, workerId);
  }

  private async handlePresenceValidationRequested(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PRESENCE_VALIDATION_REQUESTED>,
  ) {
    const { validationId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.PRESENCE_VALIDATION_REQUESTED];
    // Presence validation typically involves workers and their managers/partners
    // Without more context, we'll log for now
    this.logger.log(`Presence validation requested: ${validationId}`);
  }

  private async handleRegistrationRequestReceived(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.REGISTRATION_REQUEST_RECEIVED>,
  ) {
    const { requestId } =
      message.payload as UpdatesEventPayloads[UpdatesEvents.REGISTRATION_REQUEST_RECEIVED];
    // Registration requests typically go to partners who can approve them
    // Without more context about which partner, we'll log for now
    this.logger.log(`Registration request received: ${requestId}`);
  }

  private emitWorkerInfoChanged(workerId: string, changes: any) {
    const workerUpdatePayload: UpdatesEventPayloads[UpdatesEvents.WORKER_UPDATED] =
      {
        workerId,
        action: 'user_info_changed',
        changes,
      };

    this.server
      .to(`worker:${workerId}`)
      .emit(UpdatesEvents.WORKER_UPDATED, workerUpdatePayload);
  }

  private async handleManagerUpdated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.MANAGER_UPDATED>,
  ) {
    const { managerId } = message.payload;
    rooms.add(`manager:${managerId}`);
  }

  private async handleManagerAssignedToProject(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.MANAGER_ASSIGNED_TO_PROJECT>,
  ) {
    const { managerId, projectId } = message.payload;
    rooms.add(`manager:${managerId}`);
    rooms.add(`project:${projectId}`);
  }

  private async handleManagerRemovedFromProject(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.MANAGER_REMOVED_FROM_PROJECT>,
  ) {
    const { managerId, projectId } = message.payload;
    rooms.add(`manager:${managerId}`);
    rooms.add(`project:${projectId}`);
    this.server.in(`manager:${managerId}`).socketsLeave(`project:${projectId}`);
    this.server.in(`project:${projectId}`).socketsLeave(`manager:${managerId}`);
  }

  private async handleManagerEmploymentChanged(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.MANAGER_EMPLOYMENT_CHANGED>,
  ) {
    const { managerId } = message.payload;
    rooms.add(`manager:${managerId}`);
  }

  private async handleUserDeleted(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.USER_DELETED>,
  ) {
    const { userId } = message.payload;
    rooms.add(`user:${userId}`);
    this.server.in(`user:${userId}`).socketsLeave(`user:${userId}`);
  }

  private async handleProjectManagerUpdated(
    rooms: Set<string>,
    message: UpdatesMessageBody<UpdatesEvents.PROJECT_MANAGER_UPDATED>,
  ) {
    const { projectId, managerId, action } = message.payload;
    rooms.add(`project:${projectId}`);
    rooms.add(`manager:${managerId}`);
    if (action === 'assigned') {
      this.server
        .in(`project:${projectId}`)
        .socketsJoin(`manager:${managerId}`);
    } else if (action === 'removed') {
      this.server
        .in(`project:${projectId}`)
        .socketsLeave(`manager:${managerId}`);
    }
  }
}
